{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Segmenting remote sensing imagery with text prompts and the Segment Anything Model (SAM)\n", "\n", "[![image](https://studiolab.sagemaker.aws/studiolab.svg)](https://studiolab.sagemaker.aws/import/github/opengeos/segment-geospatial/blob/main/docs/examples/text_prompts.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/segment-geospatial/blob/main/docs/examples/text_prompts.ipynb)\n", "\n", "This notebook shows how to generate object masks from text prompts with the Segment Anything Model (SAM). \n", "\n", "Make sure you use GPU runtime for this notebook. For Google Colab, go to `Runtime` -> `Change runtime type` and select `GPU` as the hardware accelerator. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Install dependencies\n", "\n", "Uncomment and run the following cell to install the required dependencies."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install segment-geospatial groundingdino-py leafmap localtileserver"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap\n", "from samgeo.common import tms_to_geotiff\n", "from samgeo.text_sam import LangSAM"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create an interactive map"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-22.17615, -51.253043], zoom=18, height=\"800px\")\n", "m.add_basemap(\"SATELLITE\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download a sample image\n", "\n", "Pan and zoom the map to select the area of interest. Use the draw tools to draw a polygon or rectangle on the map"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bbox = m.user_roi_bounds()\n", "if bbox is None:\n", "    bbox = [-51.2565, -22.1777, -51.2512, -22.175]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image = \"Image.tif\"\n", "tms_to_geotiff(output=image, bbox=bbox, zoom=19, source=\"Satellite\", overwrite=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can also use your own image. Uncomment and run the following cell to use your own image."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# image = '/path/to/your/own/image.tif'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Display the downloaded image on the map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.layers[-1].visible = False\n", "m.add_raster(image, layer_name=\"Image\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize LangSAM class\n", "\n", "The initialization of the LangSAM class might take a few minutes. The initialization downloads the model weights and sets up the model for inference."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam = LangSAM()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Specify text prompts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text_prompt = \"tree\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Segment the image\n", "\n", "Part of the model prediction includes setting appropriate thresholds for object detection and text association with the detected objects. These threshold values range from 0 to 1 and are set while calling the predict method of the LangSAM class.\n", "\n", "`box_threshold`: This value is used for object detection in the image. A higher value makes the model more selective, identifying only the most confident object instances, leading to fewer overall detections. A lower value, conversely, makes the model more tolerant, leading to increased detections, including potentially less confident ones.\n", "\n", "`text_threshold`: This value is used to associate the detected objects with the provided text prompt. A higher value requires a stronger association between the object and the text prompt, leading to more precise but potentially fewer associations. A lower value allows for looser associations, which could increase the number of associations but also introduce less precise matches.\n", "\n", "Remember to test different threshold values on your specific data. The optimal threshold can vary depending on the quality and nature of your images, as well as the specificity of your text prompts. Make sure to choose a balance that suits your requirements, whether that's precision or recall."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam.predict(image, text_prompt, box_threshold=0.24, text_threshold=0.24)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize the results\n", "\n", "Show the result with bounding boxes on the map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam.show_anns(\n", "    cmap=\"Greens\",\n", "    box_color=\"red\",\n", "    title=\"Automatic Segmentation of Trees\",\n", "    blend=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/ytKMTlA.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Show the result without bounding boxes on the map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam.show_anns(\n", "    cmap=\"Greens\",\n", "    add_boxes=False,\n", "    alpha=0.5,\n", "    title=\"Automatic Segmentation of Trees\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/3Iq2kt1.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Show the result as a grayscale image."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam.show_anns(\n", "    cmap=\"Greys_r\",\n", "    add_boxes=False,\n", "    alpha=1,\n", "    title=\"Automatic Segmentation of Trees\",\n", "    blend=False,\n", "    output=\"trees.tif\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/KtHwFbF.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Convert the result to a vector format.  "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam.raster_to_vector(\"trees.tif\", \"trees.shp\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Show the results on the interactive map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.add_raster(\"trees.tif\", layer_name=\"Trees\", palette=\"Greens\", opacity=0.5, nodata=0)\n", "style = {\n", "    \"color\": \"#3388ff\",\n", "    \"weight\": 2,\n", "    \"fillColor\": \"#7c4185\",\n", "    \"fillOpacity\": 0.5,\n", "}\n", "m.add_vector(\"trees.shp\", layer_name=\"Vector\", style=style)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Interactive segmentation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam.show_map()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/wydt5Xt.gif)"]}], "metadata": {"kernelspec": {"display_name": "sam", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}