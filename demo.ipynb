{"cells": [{"cell_type": "code", "execution_count": 12, "id": "01978c76", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting xarray\n", "  Using cached xarray-2025.9.1-py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: numpy>=1.26 in c:\\users\\<USER>\\anaconda3\\envs\\segu\\lib\\site-packages (from xarray) (2.1.2)\n", "Requirement already satisfied: packaging>=24.1 in c:\\users\\<USER>\\anaconda3\\envs\\segu\\lib\\site-packages (from xarray) (25.0)\n", "Requirement already satisfied: pandas>=2.2 in c:\\users\\<USER>\\anaconda3\\envs\\segu\\lib\\site-packages (from xarray) (2.3.3)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\anaconda3\\envs\\segu\\lib\\site-packages (from pandas>=2.2->xarray) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\anaconda3\\envs\\segu\\lib\\site-packages (from pandas>=2.2->xarray) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in c:\\users\\<USER>\\anaconda3\\envs\\segu\\lib\\site-packages (from pandas>=2.2->xarray) (2025.2)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\anaconda3\\envs\\segu\\lib\\site-packages (from python-dateutil>=2.8.2->pandas>=2.2->xarray) (1.17.0)\n", "Using cached xarray-2025.9.1-py3-none-any.whl (1.4 MB)\n", "Installing collected packages: xarray\n", "Successfully installed xarray-2025.9.1\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install xarray"]}, {"cell_type": "code", "execution_count": 7, "id": "80beff3a", "metadata": {}, "outputs": [], "source": ["import leafmap\n", "from samgeo import samgeo2"]}, {"cell_type": "code", "execution_count": null, "id": "df1bd4a5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "segu", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}