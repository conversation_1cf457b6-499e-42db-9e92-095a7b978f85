{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Segment Anything Model for Geospatial Data \n", "\n", "[![image](https://studiolab.sagemaker.aws/studiolab.svg)](https://studiolab.sagemaker.aws/import/github/opengeos/segment-geospatial/blob/main/docs/examples/satellite.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/segment-geospatial/blob/main/docs/examples/satellite.ipynb)\n", "\n", "This notebook shows how to use segment satellite imagery using the Segment Anything Model (SAM) with a few lines of code. \n", "\n", "Make sure you use GPU runtime for this notebook. For Google Colab, go to `Runtime` -> `Change runtime type` and select `GPU` as the hardware accelerator. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Install dependencies\n", "\n", "Uncomment and run the following cell to install the required dependencies.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "outputId": "6924c5c8-39a0-4ac6-f114-9f1f8d102e88"}, "outputs": [], "source": ["# %pip install segment-geospatial"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap\n", "from samgeo import SamGeo\n", "from samgeo.common import tms_to_geotiff"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create an interactive map"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[29.676840, -95.369222], zoom=19)\n", "m.add_basemap(\"SATELLITE\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Pan and zoom the map to select the area of interest. Use the draw tools to draw a polygon or rectangle on the map"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if m.user_roi_bounds() is not None:\n", "    bbox = m.user_roi_bounds()\n", "else:\n", "    bbox = [-95.3704, 29.6762, -95.368, 29.6775]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download map tiles\n", "\n", "Download maps tiles and mosaic them into a single GeoTIFF file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image = \"satellite.tif\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Besides the `satellite` basemap, you can use any of the following basemaps returned by the `get_basemaps()` function:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# get_basemaps().keys()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Specify the basemap as the source."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tms_to_geotiff(output=image, bbox=bbox, zoom=20, source=\"Satellite\", overwrite=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can also use your own image. Uncomment and run the following cell to use your own image."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# image = '/path/to/your/own/image.tif'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Display the downloaded image on the map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.layers[-1].visible = False  # turn off the basemap\n", "m.add_raster(image, layer_name=\"Image\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/KAm84IY.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize SAM class"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam = SamGeo(\n", "    model_type=\"vit_h\",\n", "    checkpoint=\"sam_vit_h_4b8939.pth\",\n", "    sam_kwargs=None,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Segment the image\n", "\n", "Set `batch=True` to segment the image in batches. This is useful for large images that cannot fit in memory."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mask = \"segment.tif\"\n", "sam.generate(\n", "    image, mask, batch=True, foreground=True, erosion_kernel=(3, 3), mask_multiplier=255\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Polygonize the raster data\n", "\n", "Save the segmentation results as a GeoPackage file."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vector = \"segment.gpkg\"\n", "sam.tiff_to_gpkg(mask, vector, simplify_tolerance=None)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can also save the segmentation results as any vector data format supported by GeoPandas."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["shapefile = \"segment.shp\"\n", "sam.tiff_to_vector(mask, shapefile)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize the results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["style = {\n", "    \"color\": \"#3388ff\",\n", "    \"weight\": 2,\n", "    \"fillColor\": \"#7c4185\",\n", "    \"fillOpacity\": 0.5,\n", "}\n", "m.add_vector(vector, layer_name=\"Vector\", style=style)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/Ysq3u7E.png)"]}], "metadata": {"accelerator": "GPU", "colab": {"provenance": []}, "gpuClass": "standard", "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 0}