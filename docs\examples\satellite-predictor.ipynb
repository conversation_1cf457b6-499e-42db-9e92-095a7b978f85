{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Segment Anything Model for Geospatial Data \n", "\n", "[![image](https://studiolab.sagemaker.aws/studiolab.svg)](https://studiolab.sagemaker.aws/import/github/opengeos/segment-geospatial/blob/main/docs/examples/satellite-predictor.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/segment-geospatial/blob/main/docs/examples/satellite-predictor.ipynb)\n", "\n", "This notebook shows how to use segment satellite imagery using the Segment Anything Model (SAM) with a few lines of code. \n", "\n", "Make sure you use GPU runtime for this notebook. For Google Colab, go to `Runtime` -> `Change runtime type` and select `GPU` as the hardware accelerator. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Install dependencies\n", "\n", "Uncomment and run the following cell to install the required dependencies.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "outputId": "6924c5c8-39a0-4ac6-f114-9f1f8d102e88"}, "outputs": [], "source": ["# %pip install segment-geospatial"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["import os\n", "import leafmap\n", "from samgeo import SamGeoPredictor\n", "from samgeo.common import tms_to_geotiff\n", "from segment_anything import sam_model_registry"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create an interactive map"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["zoom = 16\n", "m = leafmap.Map(center=[45, -123], zoom=zoom)\n", "m.add_basemap(\"SATELLITE\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Pan and zoom the map to select the area of interest. Use the draw tools to draw a polygon or rectangle on the map"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["if m.user_roi_bounds() is not None:\n", "    bbox = m.user_roi_bounds()\n", "else:\n", "    bbox = [-123.0127, 44.9957, -122.9874, 45.0045]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download map tiles\n", "\n", "Download maps tiles and mosaic them into a single GeoTIFF file"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["image = \"satellite.tif\"\n", "# image = '/path/to/your/own/image.tif'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Besides the `satellite` basemap, you can use any of the following basemaps returned by the `get_basemaps()` function:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# get_basemaps().keys()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Specify the basemap as the source."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tms_to_geotiff(\n", "    output=image, bbox=bbox, zoom=zoom + 1, source=\"Satellite\", overwrite=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.add_raster(image, layer_name=\"Image\")\n", "m"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["Use the draw tools to draw a rectangle from which to subset segmentations on the map"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["if m.user_roi_bounds() is not None:\n", "    clip_box = m.user_roi_bounds()\n", "else:\n", "    clip_box = [-123.0064, 44.9988, -123.0005, 45.0025]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["clip_box"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize SamGeoPredictor class"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["out_dir = os.path.join(os.path.expanduser(\"~\"), \"Downloads\")\n", "checkpoint = os.path.join(out_dir, \"sam_vit_h_4b8939.pth\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["import cv2\n", "\n", "img_arr = cv2.imread(image)\n", "\n", "model_type = \"vit_h\"\n", "\n", "sam = sam_model_registry[model_type](checkpoint=checkpoint)\n", "\n", "predictor = SamGeoPredictor(sam)\n", "\n", "predictor.set_image(img_arr)\n", "\n", "masks, _, _ = predictor.predict(src_fp=image, geo_box=clip_box)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["masks_img = \"preds.tif\"\n", "predictor.masks_to_geotiff(image, masks_img, masks.astype(\"uint8\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["vector = \"feats.geojson\"\n", "gdf = predictor.geotiff_to_geojson(masks_img, vector, bidx=1)\n", "gdf.plot()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize the results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["style = {\n", "    \"color\": \"#3388ff\",\n", "    \"weight\": 2,\n", "    \"fillColor\": \"#7c4185\",\n", "    \"fillOpacity\": 0.5,\n", "}\n", "m.add_vector(vector, layer_name=\"Vector\", style=style)\n", "m"]}], "metadata": {"accelerator": "GPU", "colab": {"provenance": []}, "gpuClass": "standard", "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 4}