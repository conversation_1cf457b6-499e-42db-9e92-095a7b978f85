{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Segmenting remote sensing imagery with FastSAM\n", "\n", "[![image](https://studiolab.sagemaker.aws/studiolab.svg)](https://studiolab.sagemaker.aws/import/github/opengeos/segment-geospatial/blob/main/docs/examples/fast_sam.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/segment-geospatial/blob/main/docs/examples/fast_sam.ipynb)\n", "\n", "FastSAM: https://github.com/CASIA-IVA-Lab/FastSAM\n", "\n", "Make sure you use GPU runtime for this notebook. For Google Colab, go to `Runtime` -> `Change runtime type` and select `GPU` as the hardware accelerator. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Install dependencies\n", "\n", "Uncomment and run the following cell to install the required dependencies."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install segment-geospatial segment-anything-fast"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap\n", "from samgeo.common import tms_to_geotiff"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create an interactive map"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-22.17615, -51.253043], zoom=18, height=\"800px\")\n", "m.add_basemap(\"SATELLITE\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download a sample image\n", "\n", "Pan and zoom the map to select the area of interest. Use the draw tools to draw a polygon or rectangle on the map"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bbox = m.user_roi_bounds()\n", "if bbox is None:\n", "    bbox = [-51.2565, -22.1777, -51.2512, -22.175]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image = \"Image.tif\"\n", "tms_to_geotiff(output=image, bbox=bbox, zoom=19, source=\"Satellite\", overwrite=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can also use your own image. Uncomment and run the following cell to use your own image."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# image = '/path/to/your/own/image.tif'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Display the downloaded image on the map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.layers[-1].visible = False\n", "m.add_raster(image, layer_name=\"Image\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize SamGeo class\n", "\n", "The initialization of the SamGeo class might take a few minutes. The initialization downloads the model weights and sets up the model for inference."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from samgeo.fast_sam import SamGeo\n", "\n", "sam = SamGeo(model=\"FastSAM-x.pt\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Set the image."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam.set_image(\"Image.tif\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Segment the image with `everything_prompt`. You can also try `point_prompt`, `box_prompt`, or `text_prompt`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam.everything_prompt(output=\"mask.tif\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Show the annotated image."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam.show_anns(\"mask.png\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/af4bj7O.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Convert the segmentation results from GeoTIFF to vector."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam.raster_to_vector(\"mask.tif\", \"mask.geojson\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Show the segmentation results on the map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.add_raster(\"mask.tif\", opacity=0.5, layer_name=\"Mask\")\n", "m.add_vector(\"mask.geojson\", layer_name=\"Mask Vector\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/LvEAMSl.png)"]}], "metadata": {"kernelspec": {"display_name": "sam", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}