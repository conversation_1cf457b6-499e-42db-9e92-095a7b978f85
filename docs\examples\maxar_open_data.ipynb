{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Segmenting satellite imagery from the Maxar Open Data Program\n", "\n", "[![image](https://studiolab.sagemaker.aws/studiolab.svg)](https://studiolab.sagemaker.aws/import/github/opengeos/segment-geospatial/blob/main/docs/examples/maxar_open_data.ipynb)\n", "[![image](https://img.shields.io/badge/Open-Planetary%20Computer-black?style=flat&logo=microsoft)](https://pccompute.westeurope.cloudapp.azure.com/compute/hub/user-redirect/git-pull?repo=https://github.com/opengeos/segment-geospatial&urlpath=lab/tree/segment-geospatial/docs/examples/maxar_open_data.ipynb&branch=main)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/segment-geospatial/blob/main/docs/examples/maxar_open_data.ipynb)\n", "\n", "This notebook shows how to segment satellite imagery from the [Maxar Open Data](https://github.com/opengeos/maxar-open-data) program for Libya floods.  \n", "\n", "Make sure you use GPU runtime for this notebook. For Google Colab, go to `Runtime` -> `Change runtime type` and select `GPU` as the hardware accelerator. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Install dependencies\n", "\n", "Uncomment and run the following cell to install the required dependencies."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install segment-geospatial"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["import leafmap\n", "from samgeo import SamGeo\n", "from samgeo.common import raster_to_vector, overlay_images"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download sample data\n", "\n", "First, let's download a sample image of Derna, Libya from [here](https://drive.google.com/file/d/1jIIC5hvSPeJEC0fbDhtxVWk2XV9AxsQD/view?usp=sharing)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["url = \"https://github.com/opengeos/datasets/releases/download/raster/Derna_sample.tif\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["leafmap.download_file(url, output=\"image.tif\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create an interactive map"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["m = leafmap.Map(height=\"600px\")\n", "m.add_basemap(\"SATELLITE\")\n", "m.add_raster(\"image.tif\", layer_name=\"Image\")\n", "m.add_layer_manager()\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize SAM class"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are several tunable parameters in automatic mask generation that control how densely points are sampled and what the thresholds are for removing low quality or duplicate masks. Additionally, generation can be automatically run on crops of the image to get improved performance on smaller objects, and post-processing can remove stray pixels and holes. Here is an example configuration that samples more masks:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["sam_kwargs = {\n", "    \"points_per_side\": 32,\n", "    \"pred_iou_thresh\": 0.86,\n", "    \"stability_score_thresh\": 0.92,\n", "    \"crop_n_layers\": 1,\n", "    \"crop_n_points_downscale_factor\": 2,\n", "    \"min_mask_region_area\": 80,\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["sam = SamGeo(\n", "    model_type=\"vit_h\",\n", "    sam_kwargs=sam_kwargs,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Segment the image"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["sam.generate(\"image.tif\", output=\"mask.tif\", foreground=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Convert raster to vector"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["raster_to_vector(\"mask.tif\", output=\"mask.shp\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Display the segmentation result\n", "\n", "First, let's show the result as a binary image."]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["sam.show_masks(cmap=\"binary_r\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Display the annotations (each mask with a random color)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["sam.show_anns(axis=\"off\", opacity=1, output=\"annotation.tif\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Compare images with a slider"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["leafmap.image_comparison(\n", "    \"image.tif\",\n", "    \"annotation.tif\",\n", "    label1=\"Image\",\n", "    label2=\"Segmentation\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Overlay the annotations on the image and use the slider to change the opacity interactively."]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["overlay_images(\"image.tif\", \"annotation.tif\", backend=\"TkAgg\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Display images on an interactive map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.add_raster(\"mask.tif\", layer_name=\"Mask\", nodata=0)\n", "m.add_raster(\"annotation.tif\", layer_name=\"Annotation\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["m.add_vector(\"mask.shp\", layer_name=\"Vector\", info_mode=None)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/kjMi8AF.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 4}