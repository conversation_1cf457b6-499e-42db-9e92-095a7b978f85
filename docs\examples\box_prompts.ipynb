{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Segmenting remote sensing imagery with box prompts\n", "\n", "[![image](https://studiolab.sagemaker.aws/studiolab.svg)](https://studiolab.sagemaker.aws/import/github/opengeos/segment-geospatial/blob/main/docs/examples/box_prompts.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/segment-geospatial/blob/main/docs/examples/box_prompts.ipynb)\n", "\n", "This notebook shows how to generate object masks from box prompts with the Segment Anything Model (SAM). \n", "\n", "Make sure you use GPU runtime for this notebook. For Google Colab, go to `Runtime` -> `Change runtime type` and select `GPU` as the hardware accelerator. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Install dependencies\n", "\n", "Uncomment and run the following cell to install the required dependencies."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install segment-geospatial"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap\n", "from samgeo.common import tms_to_geotiff\n", "from samgeo import SamGeo"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create an interactive map"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-22.17615, -51.253043], zoom=18, height=\"800px\")\n", "m.add_basemap(\"SATELLITE\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download a sample image\n", "\n", "Pan and zoom the map to select the area of interest. Use the draw tools to draw a polygon or rectangle on the map"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bbox = m.user_roi_bounds()\n", "if bbox is None:\n", "    bbox = [-51.2565, -22.1777, -51.2512, -22.175]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image = \"Image.tif\"\n", "tms_to_geotiff(output=image, bbox=bbox, zoom=19, source=\"Satellite\", overwrite=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can also use your own image. Uncomment and run the following cell to use your own image."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# image = '/path/to/your/own/image.tif'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Display the downloaded image on the map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.layers[-1].visible = False\n", "m.add_raster(image, layer_name=\"Image\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize SAM class\n", "\n", "Set `automatic=False` to disable the `SamAutomaticMaskGenerator` and enable the `SamPredictor`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam = SamGeo(\n", "    model_type=\"vit_h\",\n", "    automatic=False,\n", "    sam_kwargs=None,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Specify the image to segment. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam.set_image(image)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Display the map. Use the drawing tools to draw some rectangles around the features you want to extract, such as trees, buildings."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create bounding boxes\n", "\n", "If no rectangles are drawn, the default bounding boxes will be used as follows:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if m.user_rois is not None:\n", "    boxes = m.user_rois\n", "else:\n", "    boxes = [\n", "        [-51.2546, -22.1771, -51.2541, -22.1767],\n", "        [-51.2538, -22.1764, -51.2535, -22.1761],\n", "    ]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Segment the image\n", "\n", "Use the `predict()` method to segment the image with specified bounding boxes. The `boxes` parameter accepts a list of bounding box coordinates in the format of [[left, bottom, right, top], [left, bottom, right, top], ...], a GeoJSON dictionary, or a file path to a GeoJSON file."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam.predict(boxes=boxes, point_crs=\"EPSG:4326\", output=\"mask.tif\", dtype=\"uint8\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Display the result\n", "\n", "Add the segmented image to the map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.add_raster(\"mask.tif\", cmap=\"viridis\", nodata=0, layer_name=\"Mask\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Use an existing vector file as box prompts\n", "\n", "Alternatively, you can specify a file path to a vector file. Let's download a sample vector file from GitHub."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["url = \"https://opengeos.github.io/data/sam/tree_boxes.geojson\"\n", "geojson = \"tree_boxes.geojson\"\n", "leafmap.download_file(url, geojson)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Display the vector data on the map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_raster(\"Image.tif\", layer_name=\"image\")\n", "style = {\n", "    \"color\": \"#ffff00\",\n", "    \"weight\": 2,\n", "    \"fillColor\": \"#7c4185\",\n", "    \"fillOpacity\": 0,\n", "}\n", "m.add_vector(geo<PERSON><PERSON>, style=style, zoom_to_layer=True, layer_name=\"Bounding boxes\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Segment image with box prompts\n", "\n", "Segment the image using the specified file path to the vector mask."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam.predict(boxes=geojson, point_crs=\"EPSG:4326\", output=\"mask2.tif\", dtype=\"uint8\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Display the segmented masks on the map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.add_raster(\"mask2.tif\", cmap=\"Greens\", nodata=0, opacity=0.5, layer_name=\"Tree masks\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/SpA2NV9.gif)"]}], "metadata": {"kernelspec": {"display_name": "sam", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 2}