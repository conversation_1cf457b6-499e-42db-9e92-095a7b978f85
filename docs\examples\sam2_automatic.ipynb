{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Automatic Mask Generation with SAM 2\n", "\n", "[![image](https://studiolab.sagemaker.aws/studiolab.svg)](https://studiolab.sagemaker.aws/import/github/opengeos/segment-geospatial/blob/main/docs/examples/sam2_automatic.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/segment-geospatial/blob/main/docs/examples/sam2_automatic.ipynb)\n", "\n", "This notebook shows how to segment objects from an image using the Segment Anything Model 2 (SAM2) with a few lines of code. \n", "\n", "Make sure you use GPU runtime for this notebook. For Google Colab, go to `Runtime` -> `Change runtime type` and select `GPU` as the hardware accelerator. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Install dependencies\n", "\n", "Uncomment and run the following cell to install the required dependencies."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install -U segment-geospatial"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap\n", "from samgeo import SamGeo2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create an interactive map"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[29.6768, -95.3692], zoom=19)\n", "m.add_basemap(\"SATELLITE\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "Pan and zoom the map to select the area of interest. Use the draw tools to draw a polygon or rectangle on the map"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if m.user_roi_bounds() is not None:\n", "    bbox = m.user_roi_bounds()\n", "else:\n", "    bbox = [-95.3704, 29.6762, -95.368, 29.6775]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download a sample image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image = \"satellite.tif\"\n", "leafmap.map_tiles_to_geotiff(\n", "    output=image, bbox=bbox, zoom=20, source=\"Satellite\", overwrite=True\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can also use your own image. Uncomment and run the following cell to use your own image."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# image = '/path/to/your/own/image.tif'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Display the downloaded image on the map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.layers[-1].visible = False\n", "m.add_raster(image, layer_name=\"Image\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize SAM class"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam2 = SamGeo2(model_id=\"sam2-hiera-large\", automatic=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Automatic mask generation\n", "\n", "Segment the image and save the results to a GeoTIFF file. Set `unique=True` to assign a unique ID to each object. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam2.generate(image)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam2.save_masks(output=\"masks.tif\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam2.show_masks(cmap=\"binary_r\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam2.show_masks(cmap=\"jet\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Show the object annotations (objects with random color) on the map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam2.show_anns(axis=\"off\", alpha=0.7, output=\"annotations.tif\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Compare images with a slider."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["leafmap.image_comparison(\n", "    \"satellite.tif\",\n", "    \"annotations.tif\",\n", "    label1=\"Satellite Image\",\n", "    label2=\"Image Segmentation\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Add image to the map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.add_raster(\"masks.tif\", colormap=\"jet\", layer_name=\"Masks\", nodata=0, opacity=0.7)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Convert the object annotations to vector format, such as GeoPackage, Shapefile, or GeoJSON."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam2.raster_to_vector(\"masks.tif\", \"masks.gpkg\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.add_vector(\"masks.gpkg\", layer_name=\"Objects\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Automatic mask generation options\n", "\n", "There are several tunable parameters in automatic mask generation that control how densely points are sampled and what the thresholds are for removing low quality or duplicate masks. Additionally, generation can be automatically run on crops of the image to get improved performance on smaller objects, and post-processing can remove stray pixels and holes. Here is an example configuration that samples more masks:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam2 = SamGeo2(\n", "    model_id=\"sam2-hiera-large\",\n", "    apply_postprocessing=False,\n", "    points_per_side=32,\n", "    points_per_batch=64,\n", "    pred_iou_thresh=0.7,\n", "    stability_score_thresh=0.92,\n", "    stability_score_offset=0.7,\n", "    crop_n_layers=1,\n", "    box_nms_thresh=0.7,\n", "    crop_n_points_downscale_factor=2,\n", "    min_mask_region_area=25.0,\n", "    use_m2m=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam2.generate(image, output=\"masks2.tif\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam2.show_masks(cmap=\"jet\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sam2.show_anns(axis=\"off\", alpha=0.7, output=\"annotations2.tif\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Compare images with a slider."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["leafmap.image_comparison(\n", "    image,\n", "    \"annotations2.tif\",\n", "    label1=\"Image\",\n", "    label2=\"Image Segmentation\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 4}